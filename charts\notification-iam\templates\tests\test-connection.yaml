apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "notification-iam.fullname" . }}-test-connection"
  labels:
    {{- include "notification-iam.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "notification-iam.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
