import secrets
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import HttpUrl
import os
from uuid import uuid4

parent_dir = os.path.abspath(os.path.abspath(os.path.dirname(__file__)) + "/../")


class Settings(BaseSettings):
    API_V1_STR: str = "/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    SERVICE_NAME: Optional[str] = "notification-iam"
    ENVIRONMENT: Optional[str] = "development"

    JWT_PUBLIC_KEY: str = open(f"{parent_dir}/rsa/public.pem", "r").read()
    JWT_PRIVATE_KEY: str = open(f"{parent_dir}/rsa/private.key", "r").read()

    MONGODB_URL: str

    JWT_SECRET: str

    SENTRY_DSN: HttpUrl

    FLUENTD_HTTP_ENDPOINT: str
    FLUENTD_HTTP_PORT: str
    FLUENTD_HTTP_TAG: str
    FLUENTD_HTTP_TAG2: str

    SENTRY_TRACES_SAMPLE_RATE: str = "0.01"

    REDIS_CLUSTER_NODES: str
    REDIS_CLUSTER_PASSWORD: str
    REDIS_CLUSTER_USERNAME: str
    REDIS_PREFIX: str

    RABBITMQ_URL: str
    AUTHORIZATION_REDIS_PREFIX: str

    WORKER_ID: str = uuid4().hex

    class Config:
        case_sensitive = True


settings = Settings()
