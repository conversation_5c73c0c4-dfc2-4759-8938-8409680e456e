import json

from ngmi_redis import RedisClient
from ngmi_authorization import AuthorizationJWT
from ngmi_logging import Logger

from .settings import settings
from db.mongo.database import MongoDBAsyncClient
from services import IamService, CacheService


logger = Logger(
    url=f"http://{settings.FLUENTD_HTTP_ENDPOINT}:{settings.FLUENTD_HTTP_PORT}/{settings.FLUENTD_HTTP_TAG}",
    aurl=f"http://{settings.FLUENTD_HTTP_ENDPOINT}:{settings.FLUENTD_HTTP_PORT}/{settings.FLUENTD_HTTP_TAG2}",
    service_name=settings.SERVICE_NAME,
    worker_id=settings.WORKER_ID,
)

redis = RedisClient(
    nodes=json.loads(settings.REDIS_CLUSTER_NODES),
    username=settings.REDIS_CLUSTER_USERNAME,
    password=settings.REDIS_CLUSTER_PASSWORD,
    logger=logger,
    prefix=settings.REDIS_PREFIX,
)

mongo_client = MongoDBAsyncClient(url=settings.MONGODB_URL)

authorization = AuthorizationJWT(
    jwt_public_key=settings.JWT_PUBLIC_KEY,
    validate_refresh_token="redis_blacklist",
    redis_client=redis,
    redis_prefix=settings.AUTHORIZATION_REDIS_PREFIX,
)

cache_service = CacheService(redis_client=redis)
iam_cache_service = cache_service  # Alias for backward compatibility


iam_service = IamService(cache_service=cache_service)
