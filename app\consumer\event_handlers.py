from datetime import datetime
from .config import iam_service, mongo_client


async def consume_iam(msg):
    message_type = msg["type"]
    sender = msg["sender"]
    receivers = msg["receivers"]
    sent_at = msg["sent_at"]
    additional_parameters = msg.get("additional_parameters", {})

    db = await mongo_client.get_session()
    sent_at_dt = datetime.strptime(sent_at, "%Y-%m-%dT%H:%M:%S.%f")

    for receiver in receivers:
        await iam_service.create_iam(
            session=db,
            message_type=message_type,
            sender=sender,
            receiver=receiver,
            sent_at=sent_at_dt,
            additional_parameters=additional_parameters,
        )

    return True
