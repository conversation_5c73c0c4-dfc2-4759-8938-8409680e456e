from datetime import datetime
from typing import List, Dict, Any, Optional
from pymongo import MongoClient
from db.mongo.repositories import IamRepository, IamTemplateRepository
from .iam_cache_service import CacheService


class IamService:
    def __init__(
        self, cache_service: CacheService, iam_notify_options: Dict[str, Any] = None
    ) -> None:
        self.iam_notify_options = iam_notify_options or {}
        self.cache_service = cache_service

    async def initialize(self, db: MongoClient) -> None:
        template_repository = IamTemplateRepository(db)
        self.iam_notify_options = await template_repository.get_notify_options()

    async def get_iams(
        self, session: MongoClient, phone_number: str, last_update: str = ""
    ) -> List[Dict[str, Any]]:

        repository = IamRepository(session)
        iams = await repository.find_unread_by_phone_number(phone_number, last_update)

        result = []
        for iam in iams:
            if notify_options := self.iam_notify_options.get(iam["type"]):
                iam["notify_options"] = notify_options
                result.append(iam)
        return result

    async def get_last_update(self, phone_number: str) -> Optional[str]:
        return await self.cache_service.get_cached_last_update(phone_number)

    async def mark_as_seen(
        self, session: MongoClient, iam_id: str, phone_number: str
    ) -> bool:

        repository = IamRepository(session)
        success = await repository.mark_as_seen(iam_id, phone_number)

        if success:
            await self.cache_service.cache_last_update(phone_number)

        return success

    async def create_iam(
        self,
        session: MongoClient,
        message_type: str,
        sender: str,
        receiver: str,
        sent_at: datetime,
        additional_parameters: Dict[str, Any] = None,
    ) -> Any:

        repository = IamRepository(session)
        result = await repository.create_iam(
            message_type=message_type,
            sender=sender,
            receiver=receiver,
            sent_at=sent_at,
            additional_parameters=additional_parameters,
        )

        await self.cache_service.cache_last_update(receiver)

        return result
