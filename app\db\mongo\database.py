import pymongo
from motor.motor_asyncio import AsyncIOMotorClient
from typing import List


class MongoDBAsyncClient:
    client: pymongo.MongoClient = None
    parameters: dict | None = None

    def __init__(
        self,
        url,
        max_idle_time_ms=60000,
        max_pool_size=50,
        models: List | None = None,
    ) -> None:
        self.url = url
        self.max_idle_time_ms = max_idle_time_ms
        self.max_pool_size = max_pool_size
        self.models = models if models else []

    async def get_session(self) -> pymongo.MongoClient:
        return self.client[self.parameters["database"]]

    async def connect(self):
        from db.mongo.repositories import IamRepository

        self.parameters = pymongo.uri_parser.parse_uri(self.url)
        self.client = AsyncIOMotorClient(
            self.url,
            maxIdleTimeMS=self.max_idle_time_ms,
            maxPoolSize=self.max_pool_size,
        )

        # Create indexes after connection
        database = self.client[self.parameters["database"]]

        await database.get_collection("iam").create_index("to")
        await database.get_collection("iam").create_index(
            [("to", 1), ("_id", 1)]
        )

        await database.get_collection("iam_templates").create_index("type")

        iam_repo = IamRepository(database)
        await iam_repo.create_indexes()

    async def disconnect(self):
        if self.client:
            self.client.close()
