from datetime import datetime
from typing import Optional
from ngmi_redis import RedisClient


class CacheService:

    def __init__(
        self, redis_client: RedisClient
    ) -> None:
        self.redis = redis_client
        self.key_prefix_iam = "IAMLastUpdate_"

    async def cache_last_update(self, phone_number: str) -> bool:
            timestamp = str(int(datetime.now().timestamp()))
            key = f"{self.key_prefix_iam}{phone_number}"
            await self.redis.set(key, timestamp)
            return True

    async def get_cached_last_update(self, phone_number: str) -> Optional[str]:
            key = f"{self.key_prefix_iam}{phone_number}"
            return await self.redis.get(key)
