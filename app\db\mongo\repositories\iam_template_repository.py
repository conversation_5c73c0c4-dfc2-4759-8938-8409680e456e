from pymongo import MongoClient
from typing import Dict, List, Optional
from pydantic import BaseModel
from ..collection import MongoCollection


class IamTemplateSchema(BaseModel):
    type: str
    notify_options: Dict


class IamTemplateRepository(MongoCollection[IamTemplateSchema]):

    def __init__(self, session: MongoClient) -> None:
        super().__init__(collection_name="iam_templates", session=session, schema=IamTemplateSchema)

    async def create_indexes(self) -> None:
        await self.session.get_collection(self.collection_name).create_index("type", unique=True)

    async def find_by_type(self, template_type: str) -> Optional[Dict]:
        return await self.find_one({"type": template_type})

    async def _get_all_templates(self) -> List[Dict]:
        return await self.find_all()

    async def _get_notify_options_map(self) -> Dict[str, Dict]:
        templates = await self._get_all_templates()
        notify_options = {}
        for template in templates:
            notify_options[template["type"]] = template["notify_options"]
        return notify_options

    async def get_notify_options(self) -> Dict[str, Dict]:
        return await self._get_notify_options_map()

    async def create_template(self, template_type: str, notify_options: Dict) -> Dict:
        template = {
            "type": template_type,
            "notify_options": notify_options
        }
        return await self.insert_one(template)

    async def update_template(self, template_type: str, notify_options: Dict) -> bool:
        return await self.update_one({"type": template_type}, {"notify_options": notify_options})
