from fastapi import FastAP<PERSON>

from httpx import ConnectError, ConnectTimeout, ReadError, ReadTimeout

import sentry_sdk
from sentry_sdk.integrations.asgi import SentryAsgiMiddleware
from sentry_sdk.integrations.httpx import HttpxIntegration
from starlette.middleware.errors import ServerErrorMiddleware

from ngmi_http import InvalidUpstreamResponse
from ngmi_logging import LoggingMiddleware


from core.config import redis, logger, mongo_client, iam_service
from core.settings import settings

from api.api_v1.api import api_router
from health import live, ready



from handlers.exception_handler import (
    global_execution_handler,
    invalid_upstream_response_exception_handler,
    httpx_read_error_exception_handler,
    httpx_read_timeout_exception_handler,
    httpx_connect_timeout_exception_handler,
    httpx_connect_error_exception_handler,
)
from contextlib import asynccontextmanager


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.setup()
    await mongo_client.connect()
    await redis.connect()

    db = await mongo_client.get_session()
    await iam_service.initialize(db)

    await logger.info("startup")
    yield
    await mongo_client.disconnect()
    await redis.disconnect()
    await logger.info("shutdown")


app = FastAPI(
    title=settings.SERVICE_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=lifespan,
)


app.add_middleware(
    ServerErrorMiddleware,
    handler=global_execution_handler,
)

app.add_middleware(
    LoggingMiddleware,
    logger=logger,
)

app.include_router(api_router, prefix=settings.API_V1_STR)
app.add_api_route("/v1/live", live)
app.add_api_route("/v1/ready", ready)

###################### Exception Handlers ##############################

app.add_exception_handler(
    InvalidUpstreamResponse, invalid_upstream_response_exception_handler
)
app.add_exception_handler(ReadError, httpx_read_error_exception_handler)
app.add_exception_handler(ReadTimeout, httpx_read_timeout_exception_handler)
app.add_exception_handler(ConnectTimeout, httpx_connect_timeout_exception_handler)
app.add_exception_handler(ConnectError, httpx_connect_error_exception_handler)


