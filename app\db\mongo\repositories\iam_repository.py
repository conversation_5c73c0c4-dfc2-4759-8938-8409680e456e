from pymongo import MongoClient
from bson.objectid import ObjectId
from datetime import datetime
from typing import List, Dict, Any

from ..collection import MongoCollection
from api.api_v1.schemas.iam import IamResponse


class IamRepository(MongoCollection[IamResponse]):

    def __init__(self, session: MongoClient) -> None:
        super().__init__(collection_name="iam", session=session, schema=IamResponse)

    async def create_indexes(self) -> None:
        await self.session.get_collection(self.collection_name).create_index(
            [("to", 1), ("received_at", 1)]
        )
        await self.session.get_collection(self.collection_name).create_index(
            [("to", 1), ("received_at", 1), ("sent_at", 1)]
        )

    async def find_unread_by_phone_number(
        self, phone_number: str, last_update_timestamp: str | None = None
    ) -> List[Dict]:

        query = {"to": phone_number, "received_at": None}
        if last_update_timestamp:
            last_update_dt = datetime.fromtimestamp(float(last_update_timestamp))
            query["sent_at"] = {"$gte": last_update_dt}

        return await self.find(query)

    async def create_iam(
        self,
        message_type: str,
        sender: str,
        receiver: str,
        sent_at: datetime,
        additional_parameters: Dict[str, Any] = None,
    ) -> Dict:

        new_iam = {
            "type": message_type,
            "sent_at": sent_at,
            "received_at": None,
            "from": sender,
            "to": receiver,
            "additional_parameters": additional_parameters or {},
        }

        return await self.insert_one(new_iam)

    async def mark_as_seen(self, iam_id: str, phone_number: str) -> bool:

        query = {"_id": ObjectId(iam_id), "to": phone_number}
        update_data = {"received_at": datetime.now()}
        return await self.update_one(query, update_data)
