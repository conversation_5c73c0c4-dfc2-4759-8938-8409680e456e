import httpx
from fastapi.requests import Request
from fastapi.responses import JSONResponse
from fastapi import status
from ngmi_http import InvalidUpstreamResponse

async def global_execution_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": "Internal server error"},
    )

async def invalid_upstream_response_exception_handler(
    request: Request, exc: InvalidUpstreamResponse
):
    return JSONResponse(
        status_code=400,
        content={
            "type": "https://my.irancell.ir/errors/third_parties/invalid_upstream_response",
            "title": "Unkown response from third party",
        },
    )

async def httpx_connect_error_exception_handler(
    request: Request, exc: httpx.ConnectError
):
    return JSONResponse(
        status_code=400,
        content={
            "type": "https://my.irancell.ir/errors/third_parties/connect_error",
            "title": "Unable to connect third party",
        },
    )


async def httpx_read_error_exception_handler(request: Request, exc: httpx.ReadError):
    return JSONResponse(
        status_code=400,
        content={
            "type": "https://my.irancell.ir/errors/third_parties/read_error",
            "title": "Unable to read from third party",
        },
    )


async def httpx_connect_timeout_exception_handler(
    request: Request, exc: httpx.ConnectTimeout
):
    return JSONResponse(
        status_code=400,
        content={
            "type": "https://my.irancell.ir/errors/third_parties/connect_timeout",
            "title": "Third Party Connection Timeout",
        },
    )


async def httpx_read_timeout_exception_handler(
    request: Request, exc: httpx.ReadTimeout
):
    return JSONResponse(
        status_code=400,
        content={
            "type": "https://my.irancell.ir/errors/third_parties/read_timeout",
            "title": "Third Party Read Timeout",
        },
    )