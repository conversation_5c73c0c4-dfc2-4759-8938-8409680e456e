apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "notification-iam.fullname" . }}
  labels:
    {{- include "notification-iam.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "notification-iam.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "notification-iam.selectorLabels" . | nindent 8 }}
    spec:
      topologySpreadConstraints:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance:  {{ include "notification-iam.serviceAccountName" . }}
        maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "notification-iam.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      hostAliases:
      - hostnames:
        - "t3ngmycell-db01"
        ip: "**************"
      - hostnames:
        - "t3ngmycell-db02"
        ip: "**************"
      - hostnames:
        - "t3ngmycell-db03"
        ip: "**************"
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:          
          {{- range $key, $val := .Values.env }}
            - name: {{ $key }}
              value: {{ $val | quote }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /v1/live
              port: {{ .Values.service.targetPort }}
            timeoutSeconds: 20
          readinessProbe:
            httpGet:
              path: /v1/ready
              port: {{ .Values.service.targetPort }}
            timeoutSeconds: 20
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
