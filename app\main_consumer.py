from fastapi import FastAP<PERSON>
from starlette.middleware.errors import ServerErrorMiddleware
from handlers.exception_handler import (
    global_execution_handler,
)
from contextlib import asynccontextmanager
from consumer.config import logger, mongo_client, rabbitmq_consumer, redis, iam_service
from consumer.settings import settings
from consumer.event_handlers import consume_iam
from consumer.health import live, ready


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.setup()
    await redis.connect()
    await mongo_client.connect()

    db = await mongo_client.get_session()
    await iam_service.initialize(db)

    await rabbitmq_consumer.start(
        event_handlers={"ngmi.iam": consume_iam}
    )
    await logger.info("startup")
    yield
    await rabbitmq_consumer.stop()
    await mongo_client.disconnect()
    await redis.disconnect()
    await logger.info("shutdown")


app = FastAPI(
    title=settings.SERVICE_NAME,
    lifespan=lifespan,
)

app.add_middleware(
    ServerErrorMiddleware,
    handler=global_execution_handler,
)
app.add_api_route("/v1/live", live)
app.add_api_route("/v1/ready", ready)
