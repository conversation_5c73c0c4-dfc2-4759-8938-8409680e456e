def iam_data(iam) -> dict:

    return {
        "id": str(iam["_id"]),
        "type": iam["type"],
        "from": iam["from"],  # Will be mapped to from_user in the service layer
        "to": iam["to"],
        "sent_at": iam["sent_at"],
        "received_at": iam["received_at"],
        "additional_parameters": iam["additional_parameters"],
    }


def iam_templates_data(iam_template) -> dict:
    return {
        "id": str(iam_template["_id"]),
        "type": iam_template["type"],
        "notify_options": iam_template["notify_options"],
    }
